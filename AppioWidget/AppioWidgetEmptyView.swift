//
//  AppioWidgetEmptyView.swift
//  Appio
//
//  Created by gondo on 15/09/2025.
//

import SwiftUI
import WidgetKit

struct AppioWidgetEmptyView: View {
    private let services = StorageManager.services

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            if services.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "app.badge")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    Text("No Services")
                        .font(.headline)
                        .foregroundColor(.primary)
                    Text("Connect to a service to get started")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Services")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                        .padding(.bottom, 4)

                    ForEach(services.prefix(5), id: \.id) { service in
                        HStack(spacing: 6) {
                            Circle()
                                .fill(.blue)
                                .frame(width: 6, height: 6)
                            Text(service.title)
                                .font(.caption)
                                .lineLimit(1)
                                .foregroundColor(.primary)
                            Spacer()
                        }
                    }

                    if services.count > 5 {
                        Text("+ \(services.count - 5) more")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .padding(.top, 2)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
            }
        }
        .padding()
    }
}

#if DEBUG
#Preview("Empty Widget", as: .systemMedium) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.empty()
}

#Preview("Empty Widget Small", as: .systemSmall) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.empty()
}

#Preview("Empty Widget Large", as: .systemLarge) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.empty()
}
#endif
